import 'package:dio/dio.dart';
import '/src/core/services/TokenRefreshInterceptor.dart';
import '/src/core/services/flutter_secure_storage.dart';
import 'get_access_token_repository_impl.dart';
import '../../domain/repository/get_access_token_repository.dart';
import '../../core/config/api_config.dart';
import '../../core/config/app_strings.dart';
import '../../core/services/exceptions.dart';
import '../../core/services/token_storage.dart';
import '../../domain/models/user.dart';
import '../../domain/repository/user_repository.dart';

class UserRepositoryImpl extends UserRepository {
  UserRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String userProfileUrl = APIConfig.userProfile;
  final SessionManager _sessionManager = SessionManager();
 final GetAccessTokenRepository _getAccessTokenRepository = GetAccessTokenRepositoryImpl();

  Future<Dio> _getDio() async {
    final token = await _sessionManager.getToken();
    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ),
    );

    dio.interceptors.add(TokenRefreshInterceptor(_getAccessTokenRepository));
    return dio;
  }

  @override
  Future<User> getUserProfile() async {
    try {
      final dio = await _getDio();
      final response = await dio.get(userProfileUrl);

      if (response.statusCode == 200) {
        return User.fromJson(response.data);
      } else if (response.statusCode == 401) {
        throw InvalidCredentialsException(
          message: invalidCredentials,
          statusCode: response.statusCode ?? 401,
        );
      } else {
        throw ApiException(
          message: response.data['message'] ?? failedToFetchUserProfile,
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;

      if (statusCode == 401) {
        throw InvalidCredentialsException(
          message: invalidCredentials,
          statusCode: statusCode ?? 401,
        );
      } else if (statusCode == 404) {
        throw ApiException(
          message: userNotFound,
          statusCode: statusCode ?? 404,
        );
      } else {
        final message = e.response?.data['message'] ?? failedToFetchUserProfile;
        throw ApiException(
          message: message,
          statusCode: e.response?.statusCode ?? 500,
        );
      }
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}