import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '/src/core/utils/helper.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/config/tab_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/enum/user_role.dart';
import '../../../domain/models/user.dart';
import '../../screens/dashboard/components/mobile_drawer.dart';

class Header extends StatelessWidget {
  final List<TabConfig> tabs;
  final int selectedTabIndex;
  final Function(int) onTabSelected;
  final User? user;
  final VoidCallback? onMenuPressed;
  final VoidCallback? onAddNewPressed;
  final VoidCallback? onNotificationPressed;
  final VoidCallback? onSettingsPressed;

  const Header({
    super.key,
    required this.tabs,
    required this.selectedTabIndex,
    required this.onTabSelected,
    required this.user,
    this.onMenuPressed,
    this.onAddNewPressed,
    this.onNotificationPressed,
    this.onSettingsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        top: Responsive.isMobile(context) ? 8 : defaultMargin,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              spreadRadius: 0,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Mobile menu button
            if (Responsive.showDrawer(context))
              IconButton(
                icon: const Icon(Icons.menu),
                onPressed:
                    onMenuPressed ??
                    () {
                      Scaffold.of(context).openDrawer();
                    },
              ),
            const SizedBox(width: 8),

            // Logo
            Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),

            // Desktop navigation items
            if (!Responsive.showDrawer(context)) ...[
              const SizedBox(width: 20),
              ...tabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tab = entry.value;
                // Skip hidden tabs
                if (tab.hidden) {
                  return const SizedBox.shrink();
                }
                // Skip reports tab for non-platform owners
                if (tab.title == reportsTab &&
                    user?.role != UserRole.platformOwner) {
                  return const SizedBox.shrink();
                }
                return _buildNavItem(
                  context,
                  tab.title,
                  isSelected: selectedTabIndex == index,
                  onTap: () => onTabSelected(index),
                );
              }),

              const Spacer(),

              // Add New Button
              if (user?.role != UserRole.platformOwner)
                _buildAddNewBtn(context),
              const SizedBox(width: defaultPadding),

              _headerIcon(
                Icons.notifications_outlined,
                onPressed: onNotificationPressed,
              ),
              const SizedBox(width: defaultPadding),
              _headerIcon(
                Icons.settings_outlined,
                onPressed: onSettingsPressed,
              ),
              const SizedBox(width: defaultPadding),
            ],
            if (Responsive.showDrawer(context)) ...[Spacer()],

            // Desktop profile info
            if (!Responsive.isMobile(context)) ...[_buildProfileInfo(context)],
            // Mobile profile (simplified)
            if (Responsive.isMobile(context)) ...[
              const SizedBox(width: defaultPadding),
              _buildUserAvatar(),
              const SizedBox(width: 8),
            ],
          ],
        ),
      ),
    );
  }

  // Getter for the mobile drawer
  Widget get mobileDrawer => MobileDrawer(
    user: user,
    selectedTab: selectedTabIndex,
    onTabSelected: (index) {},
    selectedTabIndex: 0,
    tabs: [],
  );

  Widget _headerIcon(IconData icon, {VoidCallback? onPressed}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(8.0),
          decoration: const BoxDecoration(
            color: AppTheme.headerIconBgColor,
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 18, color: Colors.grey[600]),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
            vertical: 8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor)
                    : AppFonts.mediumTextStyle(14, color: AppTheme.black),
              ),
              // TODO: Remove after implementing proper selection line
              // const SizedBox(height: 4),
              // Container(
              //   height: 2,
              //   width: 20,
              //   decoration: BoxDecoration(
              //     color: isSelected
              //         ? AppTheme.primaryColor
              //         : Colors.transparent,
              //     borderRadius: BorderRadius.circular(1),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewBtn(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.roundIconColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
      ),
      onPressed:
          onAddNewPressed ??
          () {
            _showAddNewPopup(context);
          },
      icon: const Icon(Icons.add, size: 16),
      label: Text(
        addNewButton,
        style: AppFonts.mediumTextStyle(12, color: Colors.white),
      ),
    );
  }

  void _showAddNewPopup(BuildContext context) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    showMenu<String>(
      context: context,
      position: position,
      color: Colors.white,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      items: [
        PopupMenuItem<String>(
          value: 'agent',
          child: _buildPopupOption(
            context,
            agentLabel,
            Icons.person_outline,
            () {},
          ),
        ),
        PopupMenuItem<String>(
          value: 'office_staff',
          child: _buildPopupOption(
            context,
            officeStaffLabel,
            Icons.business_center_outlined,
            () {},
          ),
        ),
      ],
    ).then((value) {
      if (value != null && context.mounted) {
        Navigator.pushNamed(context, '/register-broker');
      }
    });
  }

  Widget _buildPopupOption(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding / 2,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppTheme.primaryTextColor,
          ),
          const SizedBox(width: defaultPadding),
          Text(
            title,
            style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryTextColor),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        _buildUserAvatar(),
        const SizedBox(width: defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user?.name ?? '',
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              Text(
                userRoleToString(user?.role ?? UserRole.none),
                style: AppFonts.regularTextStyle(12, color: Colors.grey[600]!),
              ),
            ],
          ),
        if (Responsive.isDesktop(context))
          const Icon(Icons.keyboard_arrow_down),
      ],
    );
  }

  Widget _buildUserAvatar() {
    return FutureBuilder<bool>(
      future: isValidImageUrl(user?.avatarUrl ?? ''),
      builder: (context, snapshot) {
        bool isValid = snapshot.data ?? false;

        return CircleAvatar(
          backgroundImage: isValid
              ? CachedNetworkImageProvider(user!.avatarUrl)
              : const AssetImage('$iconAssetpath/agent_round.png'),
          radius: 16,
        );
      },
    );
  }
}
